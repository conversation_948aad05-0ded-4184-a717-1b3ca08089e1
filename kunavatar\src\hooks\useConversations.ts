'use client';

import { useState, useEffect, useCallback } from 'react';
import { Conversation } from '@/lib/database';

/**
 * 轻量级的conversations hook，专门用于侧边栏等组件获取对话列表
 * 与useConversationManager不同，这个hook只关注获取对话列表，不处理复杂的对话管理逻辑
 */
export function useConversations() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasLoaded, setHasLoaded] = useState(false);

  // 加载对话列表
  const loadConversations = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('accessToken');
      if (!token) {
        // 没有token时，设置为空数组而不是报错
        setConversations([]);
        setHasLoaded(true);
        return;
      }

      const response = await fetch('/api/conversations', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取对话列表失败');
      }

      const data = await response.json();

      if (data.success) {
        setConversations(data.conversations || []);
        setHasLoaded(true);
        if (forceRefresh) {
          console.log(`🔄 强制刷新对话列表，获取到 ${data.conversations?.length || 0} 个对话`);
        }
      } else {
        throw new Error(data.error || '获取对话列表失败');
      }
    } catch (err) {
      console.error('加载对话列表失败:', err);
      setError(err instanceof Error ? err.message : '网络错误');
      // 出错时也设置为空数组，确保UI能正常显示
      setConversations([]);
      setHasLoaded(true);
    } finally {
      setLoading(false);
    }
  }, []);

  // 组件挂载时自动加载
  useEffect(() => {
    if (!hasLoaded) {
      loadConversations();
    }
  }, [hasLoaded, loadConversations]);

  // 获取最新的对话（按updated_at排序的第一个）
  const getLatestConversation = useCallback(() => {
    return conversations && conversations.length > 0 ? conversations[0] : null;
  }, [conversations]);

  return {
    conversations,
    loading,
    error,
    hasLoaded,
    loadConversations,
    getLatestConversation,
  };
}