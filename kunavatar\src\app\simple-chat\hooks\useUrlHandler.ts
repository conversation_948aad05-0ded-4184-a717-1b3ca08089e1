'use client';

import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { CustomModel } from '@/lib/database/custom-models';

interface UseUrlHandlerProps {
  models: CustomModel[];
  selectedModel: string;
  currentConversation: any;
  conversationLoading: boolean;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (id: number) => Promise<void>;
  setSelectedModel: (model: string) => void;
}

export function useUrlHandler({
  models,
  selectedModel,
  currentConversation,
  conversationLoading,
  createConversation,
  switchConversation,
  setSelectedModel,
}: UseUrlHandlerProps) {
  const searchParams = useSearchParams();
  const [isProcessingUrl, setIsProcessingUrl] = useState(false);
  const [recentlyCreatedId, setRecentlyCreatedId] = useState<number | null>(null);
  
  // 使用ref存储函数引用，避免useEffect依赖问题
  const createConversationRef = useRef(createConversation);
  const switchConversationRef = useRef(switchConversation);
  const setSelectedModelRef = useRef(setSelectedModel);
  
  // 更新ref的值
  useEffect(() => {
    createConversationRef.current = createConversation;
    switchConversationRef.current = switchConversation;
    setSelectedModelRef.current = setSelectedModel;
  }, [createConversation, switchConversation, setSelectedModel]);

  // URL处理逻辑
  useEffect(() => {
    const handleUrlChange = async () => {
      if (isProcessingUrl) return;
      
      const shouldCreateNew = searchParams.get('new') === 'true';
      const conversationId = searchParams.get('id');
      const modelParam = searchParams.get('model');
      
      // 处理模型参数
      if (modelParam && models.length > 0) {
        const decodedModel = decodeURIComponent(modelParam);
        // 检查模型是否存在于可用模型列表中
        const modelExists = models.some(model => model.base_model === decodedModel);
        if (modelExists && selectedModel !== decodedModel) {
          console.log('🎯 从URL设置模型:', decodedModel);
          setSelectedModelRef.current(decodedModel);
        }
      }
      
      if (shouldCreateNew && models.length > 0 && selectedModel && !currentConversation && !conversationLoading && !isProcessingUrl) {
        setIsProcessingUrl(true);
        const title = `新对话 - ${new Date().toLocaleString('zh-CN', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}`;
        
        try {
          console.log('🆕 URL处理器 - 创建新对话');
          // 使用当前选择的模型（可能来自URL参数）
          const modelToUse = modelParam && models.some(m => m.base_model === decodeURIComponent(modelParam))
            ? decodeURIComponent(modelParam)
            : selectedModel;
          
          const newConversationId = await createConversationRef.current(title, modelToUse);
          if (newConversationId) {
            // 记录最近创建的对话ID，避免重复切换
            setRecentlyCreatedId(newConversationId);
            
            // 更新URL，保留model参数，但不触发切换逻辑
            const newUrl = `/simple-chat?id=${newConversationId}${modelParam ? `&model=${modelParam}` : ''}`;
            if (typeof window !== 'undefined') {
              window.history.replaceState(null, '', newUrl);
            }
            console.log(`✅ URL处理器 - 新对话创建成功，ID: ${newConversationId}`);
            // 创建对话后不需要再次切换，因为createConversation已经设置了currentConversation
            return;
          }
        } catch (err) {
          console.error('自动创建对话失败:', err);
          throw new Error('自动创建对话失败');
        } finally {
          setIsProcessingUrl(false);
        }
      } else if (conversationId && !conversationLoading && !isProcessingUrl && !shouldCreateNew) {
        const id = parseInt(conversationId);
        
        // 如果是最近创建的对话，跳过切换逻辑
        if (id === recentlyCreatedId) {
          console.log(`⏭️ URL处理器 - 跳过切换到最近创建的对话 ${id}`);
          setRecentlyCreatedId(null); // 清除标记
          return;
        }
        
        if (id && (!currentConversation || currentConversation.id !== id)) {
          setIsProcessingUrl(true);
          try {
            console.log(`🔄 URL处理器 - 切换到对话 ${id}`);
            await switchConversationRef.current(id);
            console.log(`✅ URL处理器 - 成功切换到对话 ${id}`);
          } catch (err) {
            // 检查是否是资源不存在的错误
            const isNotFoundError = (err instanceof Error && err.message.includes('资源不存在')) ||
                                   (typeof err === 'object' && err !== null && 'error' in err && 
                                    (err as any).error === '资源不存在');
            
            if (isNotFoundError) {
              console.log(`⚠️ URL处理器 - 对话 ${id} 不存在，已清除URL参数`);
            } else {
              console.error('加载指定对话失败:', err);
            }
            
            // 如果对话不存在，清除URL中的ID参数，避免循环错误
            if (typeof window !== 'undefined') {
              const newUrl = `/simple-chat${modelParam ? `?model=${modelParam}` : ''}`;
              window.history.replaceState(null, '', newUrl);
            }
            
            // 不再抛出错误，让应用继续正常运行
            console.log('已清除无效的对话ID，应用将继续正常运行');
            return;
          } finally {
            setIsProcessingUrl(false);
          }
        }
      }
    };

    handleUrlChange().catch(error => {
      console.error('URL处理失败:', error);
    });
  }, [searchParams, models, selectedModel, currentConversation, conversationLoading, isProcessingUrl]);

  return {
    isProcessingUrl,
    setIsProcessingUrl,
    recentlyCreatedId,
  };
}