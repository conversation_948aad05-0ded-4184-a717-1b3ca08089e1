'use client';

import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { CustomModel } from '@/lib/database/custom-models';

interface UseUrlHandlerProps {
  models: CustomModel[];
  selectedModel: string;
  currentConversation: any;
  conversationLoading: boolean;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (id: number) => Promise<void>;
  setSelectedModel: (model: string) => void;
}

export function useUrlHandler({
  models,
  selectedModel,
  currentConversation,
  conversationLoading,
  createConversation,
  switchConversation,
  setSelectedModel,
}: UseUrlHandlerProps) {
  const searchParams = useSearchParams();
  const [isProcessingUrl, setIsProcessingUrl] = useState(false);
  const [recentlyCreatedId, setRecentlyCreatedId] = useState<number | null>(null);
  const [hasInitialized, setHasInitialized] = useState(false);

  // 使用ref存储函数引用，避免useEffect依赖问题
  const createConversationRef = useRef(createConversation);
  const switchConversationRef = useRef(switchConversation);
  const setSelectedModelRef = useRef(setSelectedModel);

  // 更新ref的值
  useEffect(() => {
    createConversationRef.current = createConversation;
    switchConversationRef.current = switchConversation;
    setSelectedModelRef.current = setSelectedModel;
  }, [createConversation, switchConversation, setSelectedModel]);

  // 初始化检查，确保URL状态干净
  useEffect(() => {
    if (!hasInitialized && typeof window !== 'undefined') {
      const currentUrl = window.location.pathname + window.location.search;
      console.log('🔍 URL处理器 - 初始化检查当前URL:', currentUrl);
      setHasInitialized(true);
    }
  }, [hasInitialized]);

  // 简化的URL处理逻辑
  useEffect(() => {
    const handleUrlChange = async () => {
      if (!hasInitialized || isProcessingUrl || !models.length || !selectedModel) return;

      const shouldCreateNew = searchParams.get('new') === 'true';
      const conversationId = searchParams.get('id');
      const modelParam = searchParams.get('model');

      console.log(`🔍 URL处理器 - 分析URL: new=${shouldCreateNew}, id=${conversationId}, model=${modelParam}`);

      // 处理模型参数
      if (modelParam) {
        const decodedModel = decodeURIComponent(modelParam);
        const modelExists = models.some(model => model.base_model === decodedModel);
        if (modelExists && selectedModel !== decodedModel) {
          console.log('🎯 从URL设置模型:', decodedModel);
          setSelectedModelRef.current(decodedModel);
        }
      }

      // 优先级1: 创建新对话
      if (shouldCreateNew && !currentConversation && !conversationLoading) {
        setIsProcessingUrl(true);
        try {
          console.log('🆕 URL处理器 - 创建新对话');
          const title = `新对话 - ${new Date().toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}`;

          const modelToUse = modelParam && models.some(m => m.base_model === decodeURIComponent(modelParam))
            ? decodeURIComponent(modelParam)
            : selectedModel;

          const newConversationId = await createConversationRef.current(title, modelToUse);
          if (newConversationId) {
            setRecentlyCreatedId(newConversationId);
            const newUrl = `/simple-chat?id=${newConversationId}${modelParam ? `&model=${modelParam}` : ''}`;
            window.history.replaceState(null, '', newUrl);
            console.log(`✅ URL处理器 - 新对话创建成功，ID: ${newConversationId}`);
          }
        } catch (err) {
          console.error('创建对话失败:', err);
        } finally {
          setIsProcessingUrl(false);
        }
        return;
      }

      // 优先级2: 切换到指定对话
      if (conversationId && !shouldCreateNew && !conversationLoading) {
        const id = parseInt(conversationId);

        // 跳过最近创建的对话
        if (id === recentlyCreatedId) {
          console.log(`⏭️ URL处理器 - 跳过最近创建的对话 ${id}`);
          setRecentlyCreatedId(null);
          return;
        }

        // 如果不是当前对话，则切换
        if (id && (!currentConversation || currentConversation.id !== id)) {
          setIsProcessingUrl(true);
          try {
            console.log(`🔄 URL处理器 - 切换到对话 ${id}`);
            await switchConversationRef.current(id);
            console.log(`✅ URL处理器 - 成功切换到对话 ${id}`);
          } catch (err) {
            const isNotFoundError = (err instanceof Error && err.message.includes('资源不存在'));

            if (isNotFoundError) {
              console.log(`⚠️ URL处理器 - 对话 ${id} 不存在，清除URL并回到空状态`);
              // 清除URL，回到空状态，使用replace避免在历史记录中留下无效URL
              window.history.replaceState(null, '', '/simple-chat');

              // 清理浏览器历史记录中可能存在的其他无效对话ID
              // 这里我们不能直接操作历史记录，但可以确保当前状态是干净的
              console.log('🧹 已清理无效的对话URL，回到空状态');
            } else {
              console.error('切换对话失败:', err);
            }
          } finally {
            setIsProcessingUrl(false);
          }
        }
      }
    };

    handleUrlChange();
  }, [hasInitialized, searchParams, models, selectedModel, currentConversation, conversationLoading, isProcessingUrl, recentlyCreatedId]);

  return {
    isProcessingUrl,
    setIsProcessingUrl,
    recentlyCreatedId,
  };
}