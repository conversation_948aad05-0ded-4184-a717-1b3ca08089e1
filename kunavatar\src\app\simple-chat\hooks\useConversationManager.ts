import { useState, useEffect, useCallback } from 'react';
import { Conversation } from '@/lib/database/types';

interface UseConversationManagerReturn {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  loading: boolean;
  error: string | null;
  
  // 操作函数
  loadConversations: () => Promise<void>;
  loadConversationsIfNeeded: () => Promise<void>;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (id: number) => Promise<void>;
  deleteConversation: (id: number) => Promise<void>;
  updateConversationTitle: (id: number, title: string) => Promise<void>;
}

export function useConversationManager(): UseConversationManagerReturn {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasLoadedConversations, setHasLoadedConversations] = useState(false);

  // 加载对话列表 - 优化：添加缓存标志，避免重复加载
  const loadConversations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('📋 开始加载对话列表');
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/conversations', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json();
      
      if (data.success) {
        setConversations(data.conversations || []);
        setHasLoadedConversations(true);
        console.log(`✅ 成功加载 ${data.conversations?.length || 0} 个对话`);
      } else {
        setError(data.error || '加载对话列表失败');
      }
    } catch (err) {
      setError('网络错误，加载对话列表失败');
      console.error('加载对话列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 延迟加载对话列表 - 仅在需要时才加载
  const loadConversationsIfNeeded = useCallback(async () => {
    if (!hasLoadedConversations) {
      await loadConversations();
    }
  }, [hasLoadedConversations, loadConversations]);

  // 创建新对话 - 优化：减少不必要的列表刷新
  const createConversation = useCallback(async (title: string, model: string): Promise<number | null> => {
    try {
      setError(null);
      console.log(`📝 开始创建对话: ${title}, 模型: ${model}`);

      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ title, model }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ 对话创建成功:`, data.conversation);
        
        // 直接添加到本地列表，避免重新加载整个列表
        setConversations(prev => [data.conversation, ...prev]);
        setCurrentConversation(data.conversation);
        setHasLoadedConversations(true); // 标记已有数据
        
        console.log(`✅ 当前对话已设置为新创建的对话`);
        return data.conversation.id;
      } else {
        console.error(`❌ 创建对话失败:`, data.error);
        setError(data.error || '创建对话失败');
        return null;
      }
    } catch (err) {
      console.error(`❌ 创建对话异常:`, err);
      setError('网络错误，创建对话失败');
      return null;
    }
  }, []);

  // 切换对话 - 优化：复用当前对话数据，减少API调用
  const switchConversation = useCallback(async (id: number) => {
    try {
      setError(null);
      
      // 如果已经是当前对话，直接返回，避免重复切换
      if (currentConversation?.id === id) {
        console.log(`⚠️ 对话 ${id} 已经是当前对话，跳过切换`);
        return;
      }
      
      console.log(`🔄 开始切换到对话 ${id}`);
      
      // 先检查是否已在conversations列表中有此对话的基本信息
      const existingConversation = conversations.find(conv => conv.id === id);
      
      if (existingConversation) {
        console.log(`✅ 使用缓存的对话信息: ${existingConversation.title}`);
        setCurrentConversation(existingConversation);
        return; // 直接返回，由useMessageLoader处理消息加载
      }
      
      // 如果没有缓存，再发起API请求
      console.log(`🌐 从服务器获取对话 ${id} 信息`);
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ 成功获取对话 ${id} 信息:`, data.conversation);
        setCurrentConversation(data.conversation);
        
        // 更新conversations列表中的对话信息
        setConversations(prev => {
          const exists = prev.find(conv => conv.id === id);
          if (!exists) {
            return [...prev, data.conversation];
          }
          return prev.map(conv => conv.id === id ? data.conversation : conv);
        });
      } else {
        console.error(`❌ 切换对话 ${id} 失败:`, data.error);
        setError(data.error || '切换对话失败');
        throw new Error(data.error || '切换对话失败');
      }
    } catch (err) {
      // 检查是否是资源不存在的错误（来自API响应或网络错误）
      const isNotFoundError = (err instanceof Error && err.message.includes('资源不存在')) ||
                             (typeof err === 'object' && err !== null && 'error' in err && 
                              (err as any).error === '资源不存在');
      
      if (isNotFoundError) {
        console.log(`⚠️ 对话 ${id} 不存在，已跳过`);
        setError(null); // 清除错误状态
        throw err; // 仍然抛出错误，让调用者处理
      } else {
        console.error(`❌ 切换对话 ${id} 异常:`, err);
        setError('网络错误，切换对话失败');
        throw err;
      }
    }
  }, [conversations, currentConversation]);

  // 删除对话 - 优化：先确保有对话列表数据
  const deleteConversation = useCallback(async (id: number) => {
    try {
      setError(null);
      
      if (!confirm('确定要删除这个对话吗？此操作无法撤销。')) {
        return;
      }
      
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 如果删除的是当前对话，清空当前对话
        if (currentConversation?.id === id) {
          setCurrentConversation(null);
        }
        // 从本地列表中移除，避免重新加载整个列表
        setConversations(prev => prev.filter(conv => conv.id !== id));
        console.log(`✅ 成功删除对话 ${id}`);
      } else {
        setError(data.error || '删除对话失败');
      }
    } catch (err) {
      setError('网络错误，删除对话失败');
      console.error('删除对话失败:', err);
    }
  }, [currentConversation]);

  // 更新对话标题
  const updateConversationTitle = useCallback(async (id: number, title: string) => {
    try {
      setError(null);
      
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ title }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 更新本地状态
        setConversations(prev => 
          prev.map(conv => 
            conv.id === id ? { ...conv, title } : conv
          )
        );
        
        // 如果是当前对话，也更新当前对话状态
        if (currentConversation?.id === id) {
          setCurrentConversation(prev => 
            prev ? { ...prev, title } : null
          );
        }
      } else {
        setError(data.error || '更新对话标题失败');
      }
    } catch (err) {
      setError('网络错误，更新对话标题失败');
      console.error('更新对话标题失败:', err);
    }
  }, [currentConversation]);

  return {
    conversations,
    currentConversation,
    loading,
    error,
    
    loadConversations,
    loadConversationsIfNeeded,
    createConversation,
    switchConversation,
    deleteConversation,
    updateConversationTitle,
  };
}