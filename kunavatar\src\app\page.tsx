'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Loading from '@/components/Loading';

export default function HomePage() {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    const handleRedirect = async () => {
      if (isRedirecting) return;
      setIsRedirecting(true);

      try {
        // 检查是否有对话历史
        const token = localStorage.getItem('accessToken');
        if (token) {
          const response = await fetch('/api/conversations', {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            const conversations = data.conversations || [];

            if (conversations.length > 0) {
              // 有对话历史，进入最新对话
              console.log('🏠 首页 - 发现对话历史，重定向到最新对话');
              router.replace(`/simple-chat?id=${conversations[0].id}`);
              return;
            }
          }
        }

        // 没有对话历史或获取失败，创建新对话
        console.log('🏠 首页 - 没有对话历史，重定向到新对话');
        router.replace('/simple-chat?new=true');
      } catch (error) {
        console.error('首页重定向失败:', error);
        // 出错时默认创建新对话
        router.replace('/simple-chat?new=true');
      }
    };

    handleRedirect();
  }, [router, isRedirecting]);

  return (
    <div className="flex h-screen bg-theme-background items-center justify-center">
      <Loading
        size="normal"
        text="Kun Avatar正在初始化，请稍等"
        showText={true}
        containerStyle={{
          padding: '3rem'
        }}
      />
    </div>
  );
}